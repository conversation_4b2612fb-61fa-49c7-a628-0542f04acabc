import asyncio
import time
import base64
import requests
import json
import os
from datetime import datetime

from typing import Any, Optional, cast
from ii_agent.controller.agent import Agent
from ii_agent.controller.tool_manager import Agent<PERSON><PERSON><PERSON><PERSON><PERSON>, ToolCallParameters
from ii_agent.controller.state import State
from ii_agent.core.event import Agent<PERSON><PERSON><PERSON>, EventType, RealtimeEvent
from ii_agent.core.event_stream import EventStream
from ii_agent.core.logger import logger
from ii_agent.llm.base import TextR<PERSON>ult, AssistantContentBlock, ThinkingBlock, TextPrompt
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.utils.constants import COMPLETE_MESSAGE
from ii_tool.tools.base import (
    ImageContent,
    TextContent,
    ToolResult,
    ToolConfirmationDetails,
)

TOOL_RESULT_INTERRUPT_MESSAGE = "[Request interrupted by user for tool use]"
AGENT_INTERRUPT_MESSAGE = "Agent interrupted by user."
TOOL_CALL_INTERRUPT_FAKE_MODEL_RSP = "[Request interrupted by user for tool use]"
AGENT_INTERRUPT_FAKE_MODEL_RSP = (
    "Agent interrupted by user. You can resume by providing a new instruction."
)


class AgentController:
    def __init__(
        self,
        agent: Agent,
        tool_manager: AgentToolManager,
        init_history: State,
        event_stream: EventStream,
        context_manager: ContextManager,
        max_turns: int = 500,
        interactive_mode: bool = True,
        config: Optional[Any] = None,
        is_sub_agent: bool = False,
        session_id: Optional[str] = None,
    ):
        """Initialize the agent.

        Args:
            agent: The agent to use
            tools: List of tools to use
            init_history: Initial history to use
            event_stream: Event stream for publishing events
            context_manager: Context manager for token counting and truncation
            max_turns: Maximum number of turns
            interactive_mode: Whether to use interactive mode
        """
        super().__init__()
        self.agent = agent
        self.tool_manager = tool_manager
        self.config = config
        self.session_id = session_id

        self.max_turns = max_turns
        self.interactive_mode = interactive_mode
        self.interrupted = False
        self.history = init_history
        self.event_stream = event_stream
        self.context_manager = context_manager
        self.is_sub_agent = is_sub_agent

        # Tool confirmation tracking
        self._pending_confirmations: dict[str, dict] = {}
        self._confirmation_responses: dict[str, dict] = {}
        
        # Track media generation tool usage
        self.media_tool_usage = {
            "generate_image": 0,
            "generate_video": 0
        }

    @property
    def state(self) -> State:
        """Return the current conversation state/history."""
        return self.history

    def add_confirmation_response(
        self, tool_call_id: str, approved: bool, alternative_instruction: str = ""
    ) -> None:
        """Add a confirmation response for a tool call."""
        self._confirmation_responses[tool_call_id] = {
            "approved": approved,
            "alternative_instruction": alternative_instruction,
        }

    def _should_auto_approve_tool(self, tool_name: str) -> bool:
        """Check if a tool should be auto-approved based on config."""
        if not self.config:
            return False

        # Check if all tools are auto-approved
        if getattr(self.config, "auto_approve_tools", False):
            return True

        # Check if this specific tool is in the allow list
        allow_tools = getattr(self.config, "allow_tools", set())
        return tool_name in allow_tools

    async def _wait_for_confirmation(
        self, tool_call_id: str, timeout: float = 300.0
    ) -> dict:
        """Wait for confirmation response for a specific tool call."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if tool_call_id in self._confirmation_responses:
                response = self._confirmation_responses.pop(tool_call_id)
                return response

            # Check for interruption
            if self.interrupted:
                return {
                    "approved": False,
                    "alternative_instruction": "Operation interrupted",
                }

            await asyncio.sleep(0.1)

        # Timeout - default to deny
        return {"approved": False, "alternative_instruction": "Confirmation timeout"}

    async def run_impl(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        instruction = tool_input.get("instruction")
        files = tool_input.get("files")
        images_data = tool_input.get("images_data")
        # Add instruction to dialog before getting model response
        image_blocks = []
        if files:
            # Add file paths to user message
            instruction = f"""{instruction}\n\nAttached files:\n"""
            for file in files:
                instruction += f" - {file}\n"
                logger.debug(f"Attached file: {file}")

        # Then process images for image data
        if images_data:
            for image_data in images_data:
                response = requests.get(image_data["url"])
                response.raise_for_status()
                base64_image = base64.b64encode(response.content).decode("utf-8")
                image_blocks.append(
                    {
                        "source": {
                            "type": "base64",
                            "media_type": image_data["content_type"],
                            "data": base64_image,
                        }
                    }
                )

        self.history.add_user_prompt(instruction, image_blocks)
        self.interrupted = False

        remaining_turns = self.max_turns
        while remaining_turns > 0:
            self.truncate_history()
            remaining_turns -= 1

            if self.interrupted:
                # Handle interruption during model generation or other operations
                await self.add_fake_assistant_turn(AGENT_INTERRUPT_FAKE_MODEL_RSP)
                return ToolResult(
                    llm_content=AGENT_INTERRUPT_MESSAGE,
                    user_display_content=AGENT_INTERRUPT_MESSAGE,
                )

            # Only show token count in debug mode, not in interactive CLI
            if not self.interactive_mode:
                logger.info(f"(Current token count: {self.count_tokens()})\n")

            model_response = await self.agent.astep(self.history)

            if len(model_response) == 0:
                model_response = [TextResult(text=COMPLETE_MESSAGE)]

            # Add the raw response to the canonical history
            self.history.add_assistant_turn(
                cast(list[AssistantContentBlock], model_response)
            )

            # Process all TextResult blocks first
            text_results = [
                item
                for item in model_response
                if isinstance(item, (TextResult, ThinkingBlock))
            ]
            for text_result in text_results:
                # Add thinking block to history if it's a ThinkingBlock
                if isinstance(text_result, ThinkingBlock):
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.AGENT_THINKING,
                            content={"text": text_result.thinking},
                        )
                    )
                else:
                    # Emit event for each TextResult to be displayed in console
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.AGENT_RESPONSE,
                            content={"text": text_result.text},
                        )
                    )

            # Handle tool calls
            pending_tool_calls = self.history.get_pending_tool_calls()

            if len(pending_tool_calls) == 0:
                # Check if any text results indicate a retry is needed
                text_prompts = [item for item in model_response if isinstance(item, TextPrompt)]
                if any(getattr(result, 'should_retry', False) for result in text_prompts):
                    logger.debug("[retry needed due to should_retry flag]")
                    self.history.message_lists = self.history.message_lists[:-1] # remove the last turn
                    continue  # Continue the loop instead of completing
                
                # No tools were called, so assume the task is complete
                logger.debug("[no tools were called]")
                # Only emit "Task completed" if there were no text results
                if not self.is_sub_agent:
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.COMPLETE,
                            content={"text": "Task completed"},
                        )
                    )
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.STATUS_UPDATE,
                            content={"status": AgentStatus.READY},
                        )
                    )
                else:
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.SUB_AGENT_COMPLETE,
                            content={"text": "Sub agent completed"},
                        )
                    )

                # Save token usage to JSON when task completes
                self._save_token_usage_to_file()
                
                return ToolResult(
                    llm_content=self.history.get_last_assistant_text_response()
                    or "Task completed",
                    user_display_content="Task completed",
                )

            # Check for interruption before tool execution
            if self.interrupted:
                # Handle interruption during tool execution
                for tool_call in pending_tool_calls:
                    await self.add_tool_call_result(
                        tool_call,
                        ToolResult(
                            llm_content=TOOL_RESULT_INTERRUPT_MESSAGE,
                            user_display_content=TOOL_RESULT_INTERRUPT_MESSAGE,
                        ),
                    )
                await self.add_fake_assistant_turn(TOOL_CALL_INTERRUPT_FAKE_MODEL_RSP)
                return ToolResult(
                    llm_content=TOOL_RESULT_INTERRUPT_MESSAGE,
                    user_display_content=TOOL_RESULT_INTERRUPT_MESSAGE,
                )

            # Execute all tool calls using batch approach
            logger.debug(f"Executing {len(pending_tool_calls)} tool(s)")

            # Handle tool confirmation and execution
            approved_tool_calls = []
            denied_tool_calls = []
            alternative_instructions = []

            for tool_call in pending_tool_calls:
                tool = self.tool_manager.get_tool(tool_call.tool_name)
                confirmation_details = tool.should_confirm_execute(tool_call.tool_input)
                await self.event_stream.add_event(
                    RealtimeEvent(
                        type=EventType.TOOL_CALL,
                        content={
                            "tool_call_id": tool_call.tool_call_id,
                            "tool_name": tool_call.tool_name,
                            "tool_input": tool_call.tool_input,
                            "tool_display_name": tool.display_name,
                        },
                    )
                )

                # Check if tool should be auto-approved
                if self._should_auto_approve_tool(tool_call.tool_name):
                    approved_tool_calls.append(tool_call)
                elif isinstance(confirmation_details, ToolConfirmationDetails):
                    # Send confirmation event and wait for response
                    await self.event_stream.add_event(
                        RealtimeEvent(
                            type=EventType.TOOL_CONFIRMATION,
                            content={
                                "tool_call_id": tool_call.tool_call_id,
                                "tool_name": tool_call.tool_name,
                                "tool_input": tool_call.tool_input,
                                "message": confirmation_details.message,
                            },
                        )
                    )

                    # Wait for confirmation response
                    confirmation_response = await self._wait_for_confirmation(
                        tool_call.tool_call_id
                    )

                    if confirmation_response["approved"]:
                        approved_tool_calls.append(tool_call)
                    else:
                        denied_tool_calls.append(tool_call)
                        if confirmation_response["alternative_instruction"]:
                            alternative_instructions.append(
                                confirmation_response["alternative_instruction"]
                            )
                else:
                    # No confirmation needed, approve by default
                    approved_tool_calls.append(tool_call)

            # Handle denied tools
            if denied_tool_calls:
                denial_message = f"Tool execution denied for: {', '.join([tc.tool_name for tc in denied_tool_calls])}"
                if alternative_instructions:
                    denial_message += f"\nAlternative instructions: {'; '.join(alternative_instructions)}"

                # Add denial results to history
                for tool_call in denied_tool_calls:
                    await self.add_tool_call_result(
                        tool_call,
                        ToolResult(
                            llm_content=denial_message,
                            user_display_content=denial_message,
                        ),
                    )

            # Execute approved tools in batch
            if approved_tool_calls:
                tool_results = await self.tool_manager.run_tools_batch(
                    approved_tool_calls
                )

                for tool_call, tool_result in zip(approved_tool_calls, tool_results):
                    # Track media generation tool usage
                    if tool_call.tool_name in self.media_tool_usage:
                        self.media_tool_usage[tool_call.tool_name] += 1
                    
                    await self.add_tool_call_result(tool_call, tool_result)

            # If all tools were denied and we have alternative instructions, add them to history
            if not approved_tool_calls and alternative_instructions:
                alt_instruction_text = (
                    "User provided alternative instructions: "
                    + "; ".join(alternative_instructions)
                )
                self.history.add_user_prompt(alt_instruction_text)

        agent_answer = "Agent did not complete after max turns"
        await self.event_stream.add_event(
            RealtimeEvent(type=EventType.COMPLETE, content={"text": agent_answer})
        )
        return ToolResult(llm_content=agent_answer, user_display_content=agent_answer)

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Agent started with instruction: {tool_input['instruction']}"

    async def run_agent_async(
        self,
        instruction: str,
        files: list[str] | None = None,
        resume: bool = False,
        images_data: list[dict[str, str]] | None = None,
        orientation_instruction: str | None = None,
    ) -> ToolResult:
        """Start a new agent run asynchronously.

        Args:
            instruction: The instruction to the agent.
            files: Optional list of files to attach
            resume: Whether to resume the agent from the previous state,
                continuing the dialog.
            orientation_instruction: Optional orientation instruction

        Returns:
            The result from the agent execution.
        """
        if not resume:
            self.history.clear()
            self.interrupted = False

        tool_input = {
            "instruction": instruction,
            "files": files,
            "images_data": images_data,
        }
        if orientation_instruction:
            tool_input["orientation_instruction"] = orientation_instruction
        return await self.run_impl(tool_input)

    def run_agent(
        self,
        instruction: str,
        files: list[str] | None = None,
        resume: bool = False,
        images_data: list[dict[str, str]] | None = None,
        orientation_instruction: str | None = None,
    ) -> ToolResult:
        """Start a new agent run synchronously.

        Args:
            instruction: The instruction to the agent.
            files: Optional list of files to attach
            resume: Whether to resume the agent from the previous state,
                continuing the dialog.
            orientation_instruction: Optional orientation instruction

        Returns:
            The result from the agent execution.
        """
        return asyncio.run(
            self.run_agent_async(
                instruction, files, resume, images_data, orientation_instruction
            )
        )

    def clear(self):
        """Clear the dialog and reset interruption state.
        Note: This does NOT clear the file manager, preserving file context.
        """
        self.history.clear()
        self.interrupted = False

    def cancel(self):
        """Cancel the agent execution."""
        self.interrupted = True
        from ii_agent.sub_agent.base import BaseAgentTool

        for tool in self.tool_manager.get_tools():
            if isinstance(tool, BaseAgentTool):
                tool.cancel()
        logger.debug("Agent cancellation requested")

    async def add_tool_call_result(
        self, tool_call: ToolCallParameters, tool_result: ToolResult
    ):
        """Add a tool call result to the history and send it to the message queue."""
        llm_content = tool_result.llm_content
        user_display_content = tool_result.user_display_content
        is_error = tool_result.is_error
        if not user_display_content:
            if isinstance(llm_content, list):
                user_display_content = [item.model_dump() for item in llm_content]
            elif isinstance(llm_content, str):
                user_display_content = llm_content
            else:
                raise ValueError(f"Unknown content type: {type(llm_content)}")

        if isinstance(llm_content, str):
            self.history.add_tool_call_result(tool_call, llm_content)
        # NOTE: the current tool output is maximum 1 text block and 1 image block
        # TODO: handle this better, may be move the logic to each LLM client
        elif isinstance(llm_content, list):
            if len(llm_content) == 1 and isinstance(llm_content[0], TextContent):
                llm_content_text = llm_content[0].text
                self.history.add_tool_call_result(tool_call, llm_content_text)
            else:
                llm_content_fmt = []
                for content in llm_content:
                    if isinstance(content, TextContent):
                        llm_content_fmt.append(
                            {
                                "type": "text",
                                "text": content.text,
                            }
                        )
                    elif isinstance(content, ImageContent):
                        llm_content_fmt.append(
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": content.mime_type,
                                    "data": content.data,
                                },
                            }
                        )
                    else:
                        raise ValueError(f"Unknown content type: {type(content)}")

                self.history.add_tool_call_result(tool_call, llm_content_fmt)
        else:
            raise ValueError(f"Unknown content type: {type(llm_content)}")

        await self.event_stream.add_event(
            RealtimeEvent(
                type=EventType.TOOL_RESULT,
                content={
                    "tool_call_id": tool_call.tool_call_id,
                    "tool_name": tool_call.tool_name,
                    "tool_input": tool_call.tool_input,
                    "result": user_display_content,
                    "is_error": is_error,
                },
            )
        )

    async def add_fake_assistant_turn(self, text: str):
        """Add a fake assistant turn to the history and send it to the message queue."""
        self.history.add_assistant_turn(
            cast(list[AssistantContentBlock], [TextResult(text=text)])
        )
        if self.interrupted:
            rsp_type = EventType.AGENT_RESPONSE_INTERRUPTED
        else:
            rsp_type = EventType.AGENT_RESPONSE

        await self.event_stream.add_event(
            RealtimeEvent(
                type=rsp_type,
                content={"text": text},
            )
        )

    def count_tokens(self) -> int:
        """Count the tokens in the current message history."""
        return self.context_manager.count_tokens(self.history.get_messages_for_llm())

    def truncate_history(self) -> None:
        """Remove oldest messages when context window limit is exceeded."""
        truncated_messages_for_llm = self.context_manager.apply_truncation_if_needed(
            self.history.get_messages_for_llm()
        )
        self.history.set_message_list(truncated_messages_for_llm)

    def compact_context(self) -> dict[str, Any]:
        """Manually compact the conversation context using truncation.

        Returns:
            Dict containing operation status and token information.
        """
        try:
            # Get current token count before compacting
            original_token_count = self.count_tokens()

            # Apply truncation regardless of current token count
            truncated_messages_for_llm = self.context_manager.apply_truncation(
                self.history.get_messages_for_llm()
            )

            # Update history with truncated messages
            self.history.set_message_list(truncated_messages_for_llm)

            # Get new token count after compacting
            new_token_count = self.count_tokens()

            return {
                "success": True,
                "original_tokens": original_token_count,
                "new_tokens": new_token_count,
                "tokens_saved": original_token_count - new_token_count,
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
            }
    
    def get_total_token_usage(self) -> dict[str, int]:
        """Get total token usage from main agent and all sub-agents.

        Returns:
            Dict containing total input_tokens, output_tokens, and total_tokens
        """
        total_usage = {
            "input_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 0,
            "sub_agents": {}
        }
        
        # Get main agent tokens
        if hasattr(self.agent, 'token_usage'):
            main_usage = self.agent.token_usage
            total_usage["input_tokens"] += main_usage.get("input_tokens", 0)
            total_usage["output_tokens"] += main_usage.get("output_tokens", 0)
            total_usage["main_agent"] = main_usage.copy()
        
        # Get sub-agent tokens from tool manager
        for tool in self.tool_manager.get_tools():
            if hasattr(tool, 'get_token_usage'):
                sub_usage = tool.get_token_usage()
                if sub_usage and any(sub_usage.values()):
                    tool_name = getattr(tool, 'name', tool.__class__.__name__)
                    total_usage["sub_agents"][tool_name] = sub_usage
                    total_usage["input_tokens"] += sub_usage.get("input_tokens", 0)
                    total_usage["output_tokens"] += sub_usage.get("output_tokens", 0)
        
        total_usage["total_tokens"] = total_usage["input_tokens"] + total_usage["output_tokens"]
        return total_usage
    
    def _save_token_usage_to_file(self):
        """Save token usage to a JSON file using session ID, accumulating across turns."""
        try:
            current_token_usage = self.get_total_token_usage()
            
            # Create tokens directory if it doesn't exist
            tokens_dir = "tokens"
            os.makedirs(tokens_dir, exist_ok=True)
            
            # Generate filename with session ID
            if self.session_id:
                filename = f"token_usage_{self.session_id}.json"
            else:
                filename = f"token_usage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            filepath = os.path.join(tokens_dir, filename)
            
            # Load existing token data if file exists
            accumulated_usage = {
                "input_tokens": 0,
                "output_tokens": 0,
                "total_tokens": 0,
                "sub_agents": {},
                "media_usage": {
                    "generate_image": 0,
                    "generate_video": 0
                },
                "turns": []
            }
            
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r') as f:
                        existing_data = json.load(f)
                        if "token_usage" in existing_data:
                            prev_usage = existing_data["token_usage"]
                            accumulated_usage["input_tokens"] = prev_usage.get("input_tokens", 0)
                            accumulated_usage["output_tokens"] = prev_usage.get("output_tokens", 0)
                            accumulated_usage["sub_agents"] = prev_usage.get("sub_agents", {})
                            accumulated_usage["turns"] = prev_usage.get("turns", [])
                            # Load existing media usage
                            if "media_usage" in prev_usage:
                                accumulated_usage["media_usage"] = prev_usage["media_usage"]
                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(f"Could not parse existing token file {filepath}: {e}")
            
            # Add current turn to history with media usage
            current_turn = {
                "turn": len(accumulated_usage["turns"]) + 1,
                "timestamp": datetime.now().isoformat(),
                "token_usage": current_token_usage,
                "media_usage": self.media_tool_usage.copy()
            }
            accumulated_usage["turns"].append(current_turn)
            
            # Accumulate totals
            accumulated_usage["input_tokens"] += current_token_usage.get("input_tokens", 0)
            accumulated_usage["output_tokens"] += current_token_usage.get("output_tokens", 0)
            
            # Accumulate sub-agent tokens
            for agent_name, agent_usage in current_token_usage.get("sub_agents", {}).items():
                if agent_name not in accumulated_usage["sub_agents"]:
                    accumulated_usage["sub_agents"][agent_name] = {
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "total_tokens": 0
                    }
                accumulated_usage["sub_agents"][agent_name]["input_tokens"] += agent_usage.get("input_tokens", 0)
                accumulated_usage["sub_agents"][agent_name]["output_tokens"] += agent_usage.get("output_tokens", 0)
                accumulated_usage["sub_agents"][agent_name]["total_tokens"] = (
                    accumulated_usage["sub_agents"][agent_name]["input_tokens"] + 
                    accumulated_usage["sub_agents"][agent_name]["output_tokens"]
                )
            
            accumulated_usage["total_tokens"] = accumulated_usage["input_tokens"] + accumulated_usage["output_tokens"]
            
            # Accumulate media usage
            accumulated_usage["media_usage"]["generate_image"] += self.media_tool_usage.get("generate_image", 0)
            accumulated_usage["media_usage"]["generate_video"] += self.media_tool_usage.get("generate_video", 0)
            
            # Calculate costs for different models (pass accumulated usage with media)
            costs = self._calculate_costs(accumulated_usage)
            
            # Create final data structure
            usage_data = {
                "session_id": self.session_id,
                "last_updated": datetime.now().isoformat(),
                "task_completed": True,
                "total_turns": len(accumulated_usage["turns"]),
                "token_usage": accumulated_usage,
                "estimated_costs": costs
            }
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(usage_data, f, indent=2)
                
            logger.debug(f"Token usage saved to {filepath} (Turn {len(accumulated_usage['turns'])})")
            
        except Exception as e:
            logger.error(f"Failed to save token usage: {str(e)}")
    
    def _calculate_costs(self, token_usage: dict) -> dict:
        """Calculate estimated costs in USD for different LLM models and media generation.
        
        Args:
            token_usage: Token usage dictionary with input_tokens and output_tokens
            
        Returns:
            Dictionary with cost estimates for different models and media generation
        """
        input_tokens = token_usage.get("input_tokens", 0)
        output_tokens = token_usage.get("output_tokens", 0)
        
        # Convert tokens to millions for pricing calculation
        input_millions = input_tokens / 1_000_000
        output_millions = output_tokens / 1_000_000
        
        costs = {}
        
        # GPT-5 pricing
        gpt5_input_cost = input_millions * 1.25  # $1.25 per 1M tokens
        gpt5_output_cost = output_millions * 10.0  # $10.00 per 1M tokens
        costs["gpt5"] = {
            "input_cost": round(gpt5_input_cost, 6),
            "output_cost": round(gpt5_output_cost, 6),
            "total_cost": round(gpt5_input_cost + gpt5_output_cost, 6),
            "currency": "USD"
        }
        
        # Gemini pricing (using <= 200k tokens pricing as default)
        gemini_input_cost = input_millions * 1.25  # $1.25 per 1M tokens (<=200k prompts)
        gemini_output_cost = output_millions * 10.0  # $10.00 per 1M tokens (<=200k prompts)
        costs["gemini"] = {
            "input_cost": round(gemini_input_cost, 6),
            "output_cost": round(gemini_output_cost, 6),
            "total_cost": round(gemini_input_cost + gemini_output_cost, 6),
            "currency": "USD",
            "note": "Pricing for prompts <= 200k tokens. Higher rates apply for >200k prompts."
        }
        
        # Sonnet 4 pricing
        sonnet4_input_cost = input_millions * 3.0  # $3.00 per 1M tokens
        sonnet4_output_cost = output_millions * 15.0  # $15.00 per 1M tokens
        costs["sonnet4"] = {
            "input_cost": round(sonnet4_input_cost, 6),
            "output_cost": round(sonnet4_output_cost, 6),
            "total_cost": round(sonnet4_input_cost + sonnet4_output_cost, 6),
            "currency": "USD"
        }
        
        # Media generation costs - use accumulated values from token_usage if available
        if "media_usage" in token_usage:
            image_count = token_usage["media_usage"].get("generate_image", 0)
            video_count = token_usage["media_usage"].get("generate_video", 0)
        else:
            # Fallback to current session values
            image_count = self.media_tool_usage.get("generate_image", 0)
            video_count = self.media_tool_usage.get("generate_video", 0)
        
        # Actual costs per generation
        image_cost_per_unit = 0.06  # $0.06 per image
        video_cost_per_unit = 0.35  # $0.35 per video
        
        media_costs = {
            "image_generation": {
                "count": image_count,
                "unit_cost": image_cost_per_unit,
                "total_cost": round(image_count * image_cost_per_unit, 6),
                "currency": "USD"
            },
            "video_generation": {
                "count": video_count,
                "unit_cost": video_cost_per_unit,
                "total_cost": round(video_count * video_cost_per_unit, 6),
                "currency": "USD"
            },
            "total_media_cost": round(
                (image_count * image_cost_per_unit) + (video_count * video_cost_per_unit), 6
            )
        }
        
        costs["media_generation"] = media_costs
        
        # Calculate grand total across all services
        total_llm_cost = max(
            costs["gpt5"]["total_cost"],
            costs["gemini"]["total_cost"], 
            costs["sonnet4"]["total_cost"]
        )  # Use max as we'll use one model
        
        costs["grand_total"] = {
            "llm_cost": total_llm_cost,
            "media_cost": media_costs["total_media_cost"],
            "total": round(total_llm_cost + media_costs["total_media_cost"], 6),
            "currency": "USD",
            "note": "LLM cost based on highest model pricing (worst case)"
        }
        
        return costs
