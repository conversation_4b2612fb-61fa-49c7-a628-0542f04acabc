import { useCallback, useEffect, useRef, useState } from 'react'
import { Textarea } from './ui/textarea'
import ButtonIcon from './button-icon'
import { Button } from './ui/button'
import { Icon } from './ui/icon'
import { useUploadFiles, type FileUploadStatus } from '@/hooks/use-upload-files'
import { getFileIconAndColor } from '@/utils/file-utils'
import { Folder, Loader2 } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip'
import {
    selectRequireClearFiles,
    setRequireClearFiles,
    selectSelectedFeature,
    setSelectedFeature,
    selectShouldFocusInput,
    setShouldFocusInput,
    useAppDispatch,
    useAppSelector
} from '@/state'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from './ui/dropdown-menu'
import { FEATURES } from '@/constants/tool'
import { useParams } from 'react-router'
import { AGENT_TYPE } from '@/typings'

interface QuestionInputProps {
    value: string
    setValue: (value: string) => void
    handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
    handleSubmit: (question: string) => void
    className?: string
    textareaClassName?: string
    placeholder?: string
    isDisabled?: boolean
    handleEnhancePrompt?: () => void
    handleCancel?: () => void
    onFilesChange?: (filesCount: number) => void
    hideSuggestions?: boolean
    hideFeatureSelector?: boolean
}

const QuestionInput = ({
    className,
    textareaClassName,
    placeholder,
    value,
    setValue,
    handleKeyDown,
    handleSubmit,
    isDisabled,
    handleEnhancePrompt,
    handleCancel,
    onFilesChange,
    hideSuggestions,
    hideFeatureSelector
}: QuestionInputProps) => {
    const dispatch = useAppDispatch()
    const requireClearFiles = useAppSelector(selectRequireClearFiles)
    const selectedFeature = useAppSelector(selectSelectedFeature)
    const shouldFocusInput = useAppSelector(selectShouldFocusInput)
    const isUploading = useAppSelector((state) => state.files.isUploading)
    const isLoading = useAppSelector((state) => state.ui.isLoading)
    const isGeneratingPrompt = useAppSelector(
        (state) => state.ui.isGeneratingPrompt
    )
    const isCreatingSession = useAppSelector(
        (state) => state.ui.isCreatingSession
    )
    const { sessionId } = useParams()

    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const fileInputRef = useRef<HTMLInputElement>(null)

    const [files, setFiles] = useState<FileUploadStatus[]>([])
    const [currentTextareaValue, setCurrentTextareaValue] = useState(value)

    const {
        handleRemoveFile,
        handleFileUploadWithSignedUrl,
        handlePastedImageUpload
    } = useUploadFiles()

    const removeFile = (fileName: string) => {
        handleRemoveFile(fileName)
        setFiles((prev) => prev.filter((file) => file.name !== fileName))
    }

    // Handle key down events with auto-scroll for Shift+Enter
    const handleKeyDownWithAutoScroll = (
        e: React.KeyboardEvent<HTMLTextAreaElement>
    ) => {
        if (!currentTextareaValue.trim() || isDisabled || isCreatingSession)
            return

        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Check if cursor is at the last line before allowing default behavior
                const textarea = textareaRef.current
                if (textarea) {
                    const cursorPosition = textarea.selectionStart
                    const text = textarea.value

                    // Check if cursor is at or near the end of the text
                    const isAtLastLine = !text
                        .substring(cursorPosition)
                        .includes('\n')

                    // Allow default behavior for Shift+Enter (new line)
                    // Only schedule auto-scroll if we're at the last line
                    if (isAtLastLine) {
                        setTimeout(() => {
                            if (textarea) {
                                textarea.scrollTop = textarea.scrollHeight
                            }
                        }, 0)
                    }
                }
            } else {
                // For Enter key, get current value from textarea and pass to handleSubmit
                e.preventDefault()
                const currentValue = textareaRef.current?.value || ''
                if (currentValue.trim()) {
                    handleSubmit(currentValue)
                    // Clear the textarea after submission
                    if (textareaRef.current && sessionId) {
                        textareaRef.current.value = ''
                        setCurrentTextareaValue('')
                    }
                }
            }
        } else {
            // Pass other key events to the original handler, but modify to work with uncontrolled input
            const modifiedEvent = {
                ...e,
                target: {
                    ...e.target,
                    value: textareaRef.current?.value || ''
                }
            } as React.KeyboardEvent<HTMLTextAreaElement>
            handleKeyDown(modifiedEvent)
        }
    }

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) return

        const filesToUpload = Array.from(e.target.files)
        await handleFileUploadWithSignedUrl(filesToUpload, setFiles)

        // Clear the input
        e.target.value = ''
    }

    // Handle clipboard paste for images
    const handlePaste = useCallback(
        async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
            const clipboardItems = e.clipboardData?.items
            if (!clipboardItems) return

            const imageItems = Array.from(clipboardItems).filter((item) =>
                item.type.startsWith('image/')
            )

            if (imageItems.length === 0) return

            // Prevent default paste behavior for images
            e.preventDefault()

            for (const item of imageItems) {
                const file = item.getAsFile()
                if (!file) continue

                // Generate a unique filename for the pasted image
                const timestamp = Date.now()
                const extension = file.type.split('/')[1] || 'png'
                const fileName = `pasted-image-${timestamp}.${extension}`

                // Create a new File object with the generated name
                const renamedFile = new File([file], fileName, {
                    type: file.type
                })

                await handlePastedImageUpload(renamedFile, fileName, setFiles)
            }
        },
        [handlePastedImageUpload, setFiles]
    )

    const handleSelectFeature = (type: string) => {
        dispatch(setSelectedFeature(type))
        setTimeout(() => {
            textareaRef.current?.focus()
        }, 300)
    }

    const removeFeature = () => {
        dispatch(setSelectedFeature(AGENT_TYPE.GENERAL))
    }

    useEffect(() => {
        if (onFilesChange) {
            onFilesChange(files.length)
        }
    }, [files, onFilesChange])

    useEffect(() => {
        if (requireClearFiles) {
            files.forEach((file) => {
                if (file.preview) URL.revokeObjectURL(file.preview)
            })
            setFiles([])

            // Reset the flag
            dispatch(setRequireClearFiles(false))
        }
    }, [requireClearFiles, dispatch, files])

    // Clean up object URLs when component unmounts
    useEffect(() => {
        return () => {
            files.forEach((file) => {
                if (file.preview) URL.revokeObjectURL(file.preview)
            })
        }
    }, [files])

    useEffect(() => {
        setValue(textareaRef.current?.value || '')
    }, [textareaRef.current?.value])

    // Add effect to sync textarea with external value changes
    useEffect(() => {
        if (textareaRef.current && textareaRef.current.value !== value) {
            textareaRef.current.value = value
            setCurrentTextareaValue(value)
        }
    }, [value])

    // Handle auto-focus when shouldFocusInput is triggered
    useEffect(() => {
        if (shouldFocusInput && textareaRef.current) {
            // Small delay to ensure DOM is ready after navigation
            setTimeout(() => {
                if (textareaRef.current?.value) {
                    textareaRef.current.value = ''
                    setCurrentTextareaValue('')
                }
                textareaRef.current?.focus()
                // Reset the focus trigger
                dispatch(setShouldFocusInput(false))
            }, 100)
        }
    }, [shouldFocusInput, dispatch])

    return (
        <div className={`relative ${className}`}>
            {files.length > 0 && (
                <div className="absolute top-4 left-4 right-2 flex items-center overflow-auto gap-2 z-10">
                    {files.map((file) => {
                        if (file.isImage && file.preview) {
                            return (
                                <div key={file.name} className="relative">
                                    <div className="size-12 rounded-lg overflow-hidden">
                                        <img
                                            src={file.preview}
                                            alt={file.name}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>

                                    {(isUploading || file.loading) && (
                                        <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-xl">
                                            <Loader2 className="size-5 text-black animate-spin" />
                                        </div>
                                    )}
                                    <button
                                        onClick={() => removeFile(file.name)}
                                        className="absolute right-1 top-1 cursor-pointer"
                                    >
                                        <Icon
                                            name="close-circle"
                                            className="size-4"
                                        />
                                    </button>
                                </div>
                            )
                        }

                        if (file.isFolder) {
                            return (
                                <div
                                    key={file.name}
                                    className="flex items-center gap-2 bg-neutral-900 text-white rounded-full px-3 py-2 border border-gray-700 shadow-sm"
                                >
                                    <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-full">
                                        {isUploading || file.loading ? (
                                            <Loader2 className="size-5 text-white animate-spin" />
                                        ) : (
                                            <Folder className="size-5 text-white" />
                                        )}
                                    </div>
                                    <div className="flex flex-col">
                                        <span className="text-sm font-medium truncate max-w-[200px]">
                                            {file.name}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                            {file.fileCount
                                                ? `${file.fileCount} ${
                                                      file.fileCount === 1
                                                          ? 'file'
                                                          : 'files'
                                                  }`
                                                : 'Folder'}
                                        </span>
                                    </div>
                                </div>
                            )
                        }

                        const { label } = getFileIconAndColor(file.name)

                        return (
                            <div
                                key={file.name}
                                className="relative flex items-center gap-2 dark:bg-grey text-white rounded-lg p-2 pr-7"
                            >
                                {isUploading ||
                                    (file.loading && (
                                        <div
                                            className={`flex items-center justify-center w-10 h-10 rounded-full`}
                                        >
                                            <Loader2 className="size-5 text-black animate-spin" />
                                        </div>
                                    ))}
                                <div className="flex flex-col text-black">
                                    <span className="text-xs font-semibold truncate max-w-[145px]">
                                        {file.name}
                                    </span>
                                    <span className="text-xs">{label}</span>
                                </div>
                                <button
                                    onClick={() => removeFile(file.name)}
                                    className="absolute right-1 top-1 cursor-pointer"
                                >
                                    <Icon
                                        name="close-circle"
                                        className="size-4"
                                    />
                                </button>
                            </div>
                        )
                    })}
                </div>
            )}
            <div className="relative">
                <Textarea
                    ref={textareaRef}
                    className={`w-full p-4 pb-[72px] rounded-xl resize-none !placeholder-black/[0.48] dark:!placeholder-white/40 !bg-grey-3 dark:!bg-black border-2 border-grey  ${
                        files.length > 0
                            ? 'pt-[72px] !min-h-[240px]'
                            : 'min-h-[167px]'
                    } max-h-[400px] ${textareaClassName}`}
                    placeholder={
                        placeholder ||
                        'Describe what you want to accomplish ...'
                    }
                    defaultValue={value}
                    onChange={(e) => {
                        const newValue = e.target.value
                        setCurrentTextareaValue(newValue)
                    }}
                    onKeyDown={handleKeyDownWithAutoScroll}
                    onPaste={handlePaste}
                />
            </div>
            <div className="absolute bottom-0 left-0 px-4 w-full">
                {!hideSuggestions && selectedFeature === AGENT_TYPE.GENERAL && (
                    <div
                        className={`flex items-center flex-wrap max-h-[50px] overflow-auto gap-x-2 gap-y-[6px] ${currentTextareaValue.trim() ? 'hidden' : ''}`}
                    >
                        {[
                            'Create a business plan for my startup',
                            'Generate pitch deck with financial projections',
                            'Build a landing page',
                            'Create marketing strategy and content calendar',
                            'Generate product demo video and script'
                        ].map((item) => (
                            <Button
                                key={item}
                                className="text-xs bg-grey px-2 py-[3px] h-[22px] rounded-full text-black"
                                onClick={() => {
                                    if (textareaRef.current) {
                                        textareaRef.current.value = item
                                        setCurrentTextareaValue(item)
                                        setTimeout(() => {
                                            textareaRef.current?.focus()
                                        }, 300)
                                    }
                                }}
                            >
                                {item}
                            </Button>
                        ))}
                    </div>
                )}
                <div className="flex items-center justify-between !bg-grey-3 dark:!bg-black py-4 mb-[2px]">
                    <div className="flex items-center gap-x-3 justify-between">
                        <ButtonIcon
                            name="link"
                            onClick={() => fileInputRef.current?.click()}
                        />
                        <input
                            ref={fileInputRef}
                            id="file-upload"
                            type="file"
                            multiple
                            className="hidden"
                            onChange={handleFileChange}
                            disabled={
                                isUploading || (sessionId ? isLoading : false)
                            }
                        />

                        <Tooltip>
                            <TooltipTrigger asChild>
                                {isGeneratingPrompt ? (
                                    <Icon
                                        name="loading"
                                        className="animate-spin size-7 fill-black dark:fill-white"
                                    />
                                ) : (
                                    <ButtonIcon
                                        name="magic-pen"
                                        onClick={() => {
                                            if (handleEnhancePrompt) {
                                                handleEnhancePrompt()
                                            }
                                        }}
                                        disabled={
                                            isGeneratingPrompt ||
                                            !currentTextareaValue.trim() ||
                                            isDisabled ||
                                            isLoading ||
                                            isUploading
                                        }
                                    />
                                )}
                            </TooltipTrigger>
                            <TooltipContent>Enhance Prompt</TooltipContent>
                        </Tooltip>

                        {!hideFeatureSelector &&
                            (selectedFeature &&
                            selectedFeature !== AGENT_TYPE.GENERAL ? (
                                <div className="flex items-center gap-[6px] bg-sky-blue text-black rounded-full text-xs px-2 h-7 pointer-events-none">
                                    <span className="text-xs font-medium">
                                        {
                                            FEATURES.find(
                                                (feature) =>
                                                    feature.type ===
                                                    selectedFeature
                                            )?.name
                                        }
                                    </span>
                                    <button
                                        onClick={removeFeature}
                                        className="cursor-pointer pointer-events-auto"
                                    >
                                        <Icon
                                            name="cancel"
                                            className="size-4 fill-black"
                                        />
                                    </button>
                                </div>
                            ) : (
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="secondary"
                                            size="icon"
                                            className={`size-7 bg-white dark:bg-sky-blue rounded-full cursor-pointer`}
                                        >
                                            <Icon
                                                name="dashboard-2"
                                                className={`size-[18px] stroke-black`}
                                            />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="w-[230px]">
                                        {FEATURES.map((feature) => (
                                            <DropdownMenuItem
                                                key={feature.name}
                                                onClick={() =>
                                                    handleSelectFeature(
                                                        feature.type
                                                    )
                                                }
                                                className="cursor-pointer"
                                            >
                                                <Icon
                                                    name={feature.icon}
                                                    className="size-5 fill-black"
                                                />
                                                {feature.name}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            ))}
                    </div>
                    {isLoading && handleCancel ? (
                        <Button
                            onClick={handleCancel}
                            className="cursor-pointer size-7 p-0 !bg-white rounded-full hover:scale-105 active:scale-95 transition-transform shadow-[0_4px_10px_rgba(0,0,0,0.2)]"
                        >
                            <div className="size-4 rounded-xs bg-black" />
                        </Button>
                    ) : (
                        <Button
                            disabled={
                                !currentTextareaValue.trim() ||
                                isDisabled ||
                                isCreatingSession
                            }
                            onClick={() => {
                                const currentValue =
                                    textareaRef.current?.value || ''
                                if (currentValue.trim()) {
                                    handleSubmit(currentValue)
                                    // Clear the textarea after submission
                                    if (textareaRef.current && sessionId) {
                                        textareaRef.current.value = ''
                                        setCurrentTextareaValue('')
                                    }
                                }
                            }}
                            className={`cursor-pointer size-7 font-semibold ${isCreatingSession ? '' : 'bg-firefly dark:bg-sky-blue'} rounded-full`}
                        >
                            {isCreatingSession ? (
                                <Icon
                                    name="loading"
                                    className="animate-spin size-7 fill-black dark:fill-white"
                                />
                            ) : (
                                <Icon
                                    name="arrow-up"
                                    className="fill-white dark:fill-black"
                                />
                            )}
                        </Button>
                    )}
                </div>
            </div>
        </div>
    )
}

export default QuestionInput
