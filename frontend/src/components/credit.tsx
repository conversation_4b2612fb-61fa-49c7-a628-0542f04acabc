import { Button } from './ui/button'
import { Icon } from './ui/icon'

const Credit = () => {
    const availableCredit = 1234
    const totalCredit = 5000

    const formatCredit = (value: number) => value.toLocaleString('en-US')

    return (
        <div className="flex flex-col items-start gap-y-4 p-6 pb-8 border-t border-grey-2/30 dark:border-white/30">
            <p className="text-xs font-semibold text-sky-blue dark:text-black bg-firefly dark:bg-sky-blue px-2 h-[22px] rounded-full flex items-center">
                Starter Plan
            </p>
            <div className="flex gap-x-2">
                <Icon name="coin" className="fill-firefly dark:fill-white" />
                <div>
                    <div>
                        <span className="font-bold text-firefly dark:text-sky-blue">
                            {formatCredit(availableCredit)}
                        </span>
                        <span className="ml-[6px] text-firefly/30 dark:text-white/30">{`/ ${formatCredit(totalCredit)}`}</span>
                    </div>
                    <p className="text-firefly dark:text-white text-xs">
                        II-Credits
                    </p>
                </div>
            </div>
            <Button
                variant="outline"
                size="xl"
                className="w-full border-firefly dark:border-sky-blue text-firefly dark:text-sky-blue"
            >
                <Icon
                    name="edit"
                    className="fill-firefly dark:fill-sky-blue size-5"
                />
                Upgrade Plan
            </Button>
        </div>
    )
}

export default Credit
