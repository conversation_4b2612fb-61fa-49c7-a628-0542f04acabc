import { useEffect, useState } from 'react'

import { But<PERSON> } from '../ui/button'
import { Icon } from '../ui/icon'
import { Sheet, SheetClose, SheetContent, SheetHeader } from '../ui/sheet'
import { Textarea } from '../ui/textarea'
import { ISetting } from '@/typings'
import { useAppSelector } from '@/state'

interface CodexSettingProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    onSaveConfig: (data: ISetting) => void
}

const CodexSetting = ({
    open,
    onOpenChange,
    onSaveConfig
}: CodexSettingProps) => {
    const isSavingSetting = useAppSelector(
        (state) => state.settings.isSavingSetting
    )
    const currentSettingData = useAppSelector(
        (state) => state.settings.currentSettingData
    )

    const [config, setConfig] = useState('')

    const handleCancel = () => {
        onOpenChange(false)
    }

    const handleSaveConfig = () => {
        // check if config is valid JSON
        let parsed: any
        try {
            parsed = JSON.parse(config)
        } catch {
            return
        }
        onSaveConfig({
            ...currentSettingData,
            codex_config: parsed
        })
    }

    useEffect(() => {
        setConfig(JSON.stringify(currentSettingData?.codex_config, null, 2))
    }, [currentSettingData])

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="px-6 pt-12 w-full !max-w-[560px]">
                <SheetHeader className="p-0 gap-6 pb-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-x-2">
                            <Icon name="codex" className="size-12" />
                            <div className="space-y-1">
                                <p className="text-2xl font-semibold dark:text-white">
                                    Codex
                                </p>
                                <p className="text-base dark:text-white/[0.56]">
                                    OpenAI
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="arrow-right"
                                    className="dark:inline hidden"
                                />
                                <Icon
                                    name="arrow-right-dark"
                                    className="dark:hidden inline"
                                />
                            </SheetClose>
                        </div>
                    </div>
                </SheetHeader>
                <div className="overflow-auto pb-12">
                    <p className="dark:text-white text-lg font-semibold">
                        About
                    </p>
                    <p className="dark:text-white text-sm mt-3">
                        Enable OpenAI Codex for autonomous code generation and
                        review
                    </p>
                    <Button
                        className="h-[22px] bg-firefly dark:bg-sky-blue-2 text-sky-blue-2 dark:text-black gap-x-[6px] mt-4 text-xs rounded-full !font-normal"
                        onClick={() =>
                            window.open(
                                `https://openai.com/vi-VN/codex/`,
                                '_blank'
                            )
                        }
                    >
                        <Icon
                            name="global"
                            className="size-4 fill-sky-blue-2 dark:fill-black"
                        />
                        Remote
                    </Button>
                    <p className="dark:text-white text-lg font-semibold mt-6">
                        JSON Configuration
                    </p>

                    <div className="space-y-2 relative mt-3">
                        <Icon
                            name="key-square"
                            className={`absolute top-3 left-4 fill-black dark:fill-white ${config ? '' : 'opacity-30'}`}
                        />
                        <Textarea
                            id="tool-config"
                            className="pl-[56px] min-h-[144px] mb-4"
                            placeholder="Enter your JSON configuration"
                            value={config}
                            onChange={(e) => setConfig(e.target.value)}
                        />
                    </div>
                    <div className="space-y-4 grid grid-cols-2 gap-4">
                        <Button
                            type="button"
                            variant="outline"
                            className="h-12 rounded-xl text-base"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="h-12 rounded-xl bg-sky-blue text-black text-base"
                            disabled={isSavingSetting}
                            onClick={() => handleSaveConfig()}
                        >
                            {isSavingSetting ? 'Saving...' : 'Save'}
                        </Button>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    )
}

export default CodexSetting
